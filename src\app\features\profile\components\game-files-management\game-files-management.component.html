<!-- Game Files Management Content -->
<div class="max-w-6xl">
  <!-- Header with Icon and Title -->
  <div class="flex items-center mb-4 lg:mb-6">
    <div class="w-10 h-10 lg:w-12 lg:h-12 bg-gradient-to-br from-blue-600 to-slate-700 rounded-full flex items-center justify-center mr-3 lg:mr-4">
      <svg class="w-5 h-5 lg:w-6 lg:h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
      </svg>
    </div>
    <div>
      <h1 class="text-xl lg:text-2xl font-bold text-white">Управление файлами игр</h1>
      <p class="text-gray-400 text-sm lg:text-base">Загрузка и управление файлами игр для различных платформ</p>
    </div>
  </div>

  <!-- Controls -->
  <div class="mb-6">
      <!-- Add Button -->
      <div class="mb-4">
        <button
          (click)="toggleAddForm()"
          class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
          {{ showAddForm ? 'Отмена' : 'Добавить файл' }}
        </button>
      </div>

    <!-- Add Form -->
    <div *ngIf="showAddForm" class="mb-6 p-4 bg-slate-800/60 backdrop-blur-md border border-slate-600/30 rounded-xl">
        <h3 class="text-lg font-semibold text-white mb-4">Добавить новый файл игры</h3>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <!-- Game Selection -->
          <div>
            <label class="block text-gray-300 text-sm font-medium mb-2">Игра *</label>
            <select
              [(ngModel)]="newGameFile.game"
              class="w-full px-3 py-2 text-sm bg-slate-700/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="0">Выберите игру</option>
              <option *ngFor="let game of games" [value]="game.id">{{ game.title }}</option>
            </select>
          </div>

          <!-- Platform Selection -->
          <div>
            <label class="block text-gray-300 text-sm font-medium mb-2">Платформа *</label>
            <select
              [(ngModel)]="newGameFile.platform"
              class="w-full px-3 py-2 text-sm bg-slate-700/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option *ngFor="let platform of platformOptions.slice(1)" [value]="platform.value">{{ platform.label }}</option>
            </select>
          </div>

          <!-- Version -->
          <div>
            <label class="block text-gray-300 text-sm font-medium mb-2">Версия</label>
            <input
              type="text"
              [(ngModel)]="newGameFile.version"
              placeholder="1.0"
              class="w-full px-3 py-2 text-sm bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>

          <!-- Description -->
          <div>
            <label class="block text-gray-300 text-sm font-medium mb-2">Описание</label>
            <input
              type="text"
              [(ngModel)]="newGameFile.description"
              placeholder="Описание файла"
              class="w-full px-3 py-2 text-sm bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>
        </div>

        <!-- File Upload -->
        <div class="mt-4">
          <label class="block text-gray-300 text-sm font-medium mb-2">Файл игры *</label>
          <input
            type="file"
            (change)="onFileSelected($event)"
            class="w-full px-3 py-2 text-sm bg-slate-700/50 border border-slate-600/50 rounded-lg text-white file:mr-3 file:py-1 file:px-3 file:rounded-lg file:border-0 file:text-xs file:font-medium file:bg-blue-600 file:text-white hover:file:bg-blue-700 file:cursor-pointer">
          <p class="text-gray-400 text-xs mt-1">Максимальный размер файла: 500MB</p>
        </div>

      <!-- Error Message -->
      <div *ngIf="addGameFileError" class="mt-4 p-3 bg-red-500/20 border border-red-500/50 rounded-lg">
        <p class="text-red-300 text-sm">{{ addGameFileError }}</p>
      </div>

        <!-- Submit Button -->
        <div class="mt-4 flex justify-end">
          <button
            (click)="onAddGameFile()"
            [disabled]="addGameFileLoading"
            class="px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white text-sm font-medium rounded-lg transition-colors duration-200">
            <span *ngIf="addGameFileLoading" class="inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></span>
            {{ addGameFileLoading ? 'Загрузка...' : 'Добавить файл' }}
          </button>
        </div>
      </div>

      <!-- Filters -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <!-- Search -->
        <div>
          <label class="block text-gray-300 text-sm font-medium mb-1">Поиск</label>
          <input
            type="text"
            [(ngModel)]="searchTerm"
            (input)="onSearchChange()"
            placeholder="Поиск по названию файла..."
            class="w-full px-3 py-2 text-sm bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>

        <!-- Game Filter -->
        <div>
          <label class="block text-gray-300 text-sm font-medium mb-1">Игра</label>
          <select
            [(ngModel)]="selectedGame"
            (change)="onGameFilterChange()"
            class="w-full px-3 py-2 text-sm bg-slate-700/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option [value]="null">Все игры</option>
            <option *ngFor="let game of games" [value]="game.id">{{ game.title }}</option>
          </select>
        </div>

        <!-- Platform Filter -->
        <div>
          <label class="block text-gray-300 text-sm font-medium mb-1">Платформа</label>
          <select
            [(ngModel)]="selectedPlatform"
            (change)="onPlatformFilterChange()"
            class="w-full px-3 py-2 text-sm bg-slate-700/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option *ngFor="let platform of platformOptions" [value]="platform.value">{{ platform.label }}</option>
          </select>
        </div>

        <!-- Active Status Filter -->
        <div>
          <label class="block text-gray-300 text-sm font-medium mb-1">Статус</label>
          <select
            [(ngModel)]="selectedActiveStatus"
            (change)="onActiveStatusFilterChange()"
            class="w-full px-3 py-2 text-sm bg-slate-700/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="all">Все</option>
            <option value="active">Активные</option>
            <option value="inactive">Неактивные</option>
          </select>
        </div>

        <!-- Sort -->
        <div>
          <label class="block text-gray-300 text-sm font-medium mb-1">Сортировка</label>
          <select
            [(ngModel)]="sortBy"
            (change)="onSortChange()"
            class="w-full px-3 py-2 text-sm bg-slate-700/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="-id">Новые первыми</option>
            <option value="id">Старые первыми</option>
            <option value="file_name">По названию файла</option>
            <option value="-file_name">По названию файла (убыв.)</option>
            <option value="platform">По платформе</option>
            <option value="-uploaded_at">По дате загрузки</option>
          </select>
        </div>
      </div>
    </div>

  <!-- Loading State -->
  <div *ngIf="gameFilesLoading" class="flex justify-center items-center py-20">
    <div class="bg-slate-800/60 backdrop-blur-md border border-slate-600/30 rounded-xl p-6">
      <svg class="animate-spin h-8 w-8 text-blue-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="gameFilesError && !gameFilesLoading" class="bg-red-500/20 border border-red-500/50 rounded-lg p-4 text-center mb-6">
    <p class="text-sm lg:text-base text-red-300 mb-3">{{ gameFilesError }}</p>
    <button
      (click)="loadGameFiles()"
      class="px-3 py-1.5 text-sm lg:text-base bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors">
      Попробовать снова
    </button>
  </div>

  <!-- Game Files List -->
  <div *ngIf="!gameFilesLoading && !gameFilesError" class="bg-slate-800/60 backdrop-blur-md border border-slate-600/30 rounded-xl overflow-hidden">
      <!-- Desktop Table -->
      <div class="hidden lg:block overflow-x-auto">
        <table class="w-full">
          <thead class="bg-slate-700/50">
            <tr>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Файл</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Игра</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Платформа</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Версия</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Размер</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Статус</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Загружен</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Действия</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-slate-700/50">
            <tr *ngFor="let gameFile of gameFiles" class="hover:bg-slate-700/30 transition-colors duration-200">
              <td class="px-4 py-3">
                <button
                  (click)="openEditModal(gameFile)"
                  class="text-left hover:text-blue-400 transition-colors duration-200">
                  <div class="text-sm font-medium text-white hover:text-blue-400">{{ gameFile.file_name }}</div>
                  <div *ngIf="gameFile.description" class="text-xs text-gray-400">{{ gameFile.description }}</div>
                </button>
              </td>
              <td class="px-4 py-3 text-sm text-gray-300">{{ getGameTitle(gameFile.game) }}</td>
              <td class="px-4 py-3 text-sm text-gray-300">{{ getPlatformLabel(gameFile.platform) }}</td>
              <td class="px-4 py-3 text-sm text-gray-300">{{ gameFile.version }}</td>
              <td class="px-4 py-3 text-sm text-gray-300">{{ formatFileSize(gameFile.file_size) }}</td>
              <td class="px-4 py-3">
                <span class="text-sm" [class]="getActiveStatusClass(gameFile.is_active)">
                  {{ getActiveStatusText(gameFile.is_active) }}
                </span>
              </td>
              <td class="px-4 py-3 text-sm text-gray-300">{{ formatDate(gameFile.uploaded_at) }}</td>
              <td class="px-4 py-3">
                <div class="flex space-x-2">
                  <button
                    (click)="downloadGameFile(gameFile)"
                    class="px-2 py-1 bg-green-600 hover:bg-green-700 text-white text-xs rounded transition-colors duration-200"
                    title="Скачать файл">
                    Скачать
                  </button>
                  <button
                    (click)="deleteGameFile(gameFile)"
                    class="px-2 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded transition-colors duration-200"
                    title="Удалить файл">
                    Удалить
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Mobile Cards -->
      <div class="lg:hidden">
        <div *ngFor="let gameFile of gameFiles" class="p-4 border-b border-slate-700/50 last:border-b-0">
          <div class="flex justify-between items-start mb-2">
            <div class="flex-1">
              <button
                (click)="openEditModal(gameFile)"
                class="text-left hover:text-blue-400 transition-colors duration-200">
                <h3 class="text-sm font-medium text-white hover:text-blue-400">{{ gameFile.file_name }}</h3>
              </button>
              <p class="text-xs text-gray-400">{{ getGameTitle(gameFile.game) }}</p>
            </div>
            <span class="text-xs px-2 py-1 rounded" [class]="getActiveStatusClass(gameFile.is_active)">
              {{ getActiveStatusText(gameFile.is_active) }}
            </span>
          </div>
          
          <div class="grid grid-cols-2 gap-2 text-xs text-gray-300 mb-3">
            <div>Платформа: {{ getPlatformLabel(gameFile.platform) }}</div>
            <div>Версия: {{ gameFile.version }}</div>
            <div>Размер: {{ formatFileSize(gameFile.file_size) }}</div>
            <div>Загружен: {{ formatDate(gameFile.uploaded_at) }}</div>
          </div>
          
          <div *ngIf="gameFile.description" class="text-xs text-gray-400 mb-3">{{ gameFile.description }}</div>
          
          <div class="flex space-x-2">
            <button
              (click)="downloadGameFile(gameFile)"
              class="px-3 py-1 bg-green-600 hover:bg-green-700 text-white text-xs rounded transition-colors duration-200">
              Скачать
            </button>
            <button
              (click)="deleteGameFile(gameFile)"
              class="px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded transition-colors duration-200">
              Удалить
            </button>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div *ngIf="gameFiles.length === 0" class="p-8 text-center">
        <p class="text-gray-400">Файлы игр не найдены</p>
      </div>
    </div>

    <!-- Pagination -->
    <div *ngIf="totalPages > 1" class="mt-6 flex justify-center">
      <nav class="flex space-x-2">
        <button
          (click)="onPageChange(currentPage - 1)"
          [disabled]="!hasPrevious"
          class="px-3 py-2 text-sm bg-slate-700 hover:bg-slate-600 disabled:bg-slate-800 disabled:text-gray-500 text-white rounded-lg transition-colors duration-200">
          Назад
        </button>
        
        <button
          *ngFor="let page of pages"
          (click)="onPageChange(page)"
          [class.bg-blue-600]="page === currentPage"
          [class.bg-slate-700]="page !== currentPage"
          class="px-3 py-2 text-sm hover:bg-slate-600 text-white rounded-lg transition-colors duration-200">
          {{ page }}
        </button>
        
        <button
          (click)="onPageChange(currentPage + 1)"
          [disabled]="!hasNext"
          class="px-3 py-2 text-sm bg-slate-700 hover:bg-slate-600 disabled:bg-slate-800 disabled:text-gray-500 text-white rounded-lg transition-colors duration-200">
          Вперед
        </button>
      </nav>
    </div>
  </div>

  <!-- Edit Game File Modal -->
  <div *ngIf="showEditModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="bg-slate-800 rounded-lg border border-slate-600 w-full max-w-md max-h-[90vh] overflow-y-auto">
      <!-- Modal Header -->
      <div class="flex items-center justify-between p-4 border-b border-slate-600">
        <h3 class="text-lg font-semibold text-white">Редактировать файл игры</h3>
        <button
          (click)="closeEditModal()"
          class="text-gray-400 hover:text-white transition-colors">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Modal Body -->
      <div class="p-4">
        <div *ngIf="editingGameFile" class="space-y-4">
          <!-- File Name (Read-only) -->
          <div>
            <label class="block text-gray-300 text-sm font-medium mb-2">Название файла</label>
            <input
              type="text"
              [value]="editingGameFile.file_name"
              readonly
              class="w-full px-3 py-2 text-sm bg-slate-700/50 border border-slate-600/50 rounded-lg text-gray-400 cursor-not-allowed">
          </div>

          <!-- Platform -->
          <div>
            <label class="block text-gray-300 text-sm font-medium mb-2">Платформа *</label>
            <select
              [(ngModel)]="editGameFileData.platform"
              class="w-full px-3 py-2 text-sm bg-slate-700/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option *ngFor="let platform of platformOptions" [value]="platform.value">{{ platform.label }}</option>
            </select>
          </div>

          <!-- Version -->
          <div>
            <label class="block text-gray-300 text-sm font-medium mb-2">Версия</label>
            <input
              type="text"
              [(ngModel)]="editGameFileData.version"
              placeholder="1.0"
              class="w-full px-3 py-2 text-sm bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>

          <!-- Description -->
          <div>
            <label class="block text-gray-300 text-sm font-medium mb-2">Описание</label>
            <textarea
              [(ngModel)]="editGameFileData.description"
              placeholder="Описание файла"
              rows="3"
              class="w-full px-3 py-2 text-sm bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"></textarea>
          </div>

          <!-- Active Status -->
          <div>
            <label class="flex items-center">
              <input
                type="checkbox"
                [(ngModel)]="editGameFileData.is_active"
                class="w-4 h-4 text-blue-600 bg-slate-700 border-slate-600 rounded focus:ring-blue-500 focus:ring-2">
              <span class="ml-2 text-sm text-gray-300">Файл активен</span>
            </label>
          </div>

          <!-- File Info (Read-only) -->
          <div class="bg-slate-700/30 rounded-lg p-3">
            <div class="text-xs text-gray-400 space-y-1">
              <div>Размер: {{ formatFileSize(editingGameFile.file_size) }}</div>
              <div>Загружен: {{ formatDate(editingGameFile.uploaded_at) }}</div>
              <div>Обновлен: {{ formatDate(editingGameFile.updated_at) }}</div>
            </div>
          </div>

          <!-- Error Message -->
          <div *ngIf="editGameFileError" class="p-3 bg-red-500/20 border border-red-500/50 rounded-lg">
            <p class="text-red-300 text-sm">{{ editGameFileError }}</p>
          </div>
        </div>
      </div>

      <!-- Modal Footer -->
      <div class="flex justify-end space-x-3 p-4 border-t border-slate-600">
        <button
          (click)="closeEditModal()"
          class="px-4 py-2 text-sm font-medium text-gray-300 hover:text-white transition-colors">
          Отмена
        </button>
        <button
          (click)="onUpdateGameFile()"
          [disabled]="editGameFileLoading"
          class="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white text-sm font-medium rounded-lg transition-colors duration-200">
          <span *ngIf="editGameFileLoading" class="inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></span>
          {{ editGameFileLoading ? 'Сохранение...' : 'Сохранить' }}
        </button>
      </div>
    </div>
  </div>
