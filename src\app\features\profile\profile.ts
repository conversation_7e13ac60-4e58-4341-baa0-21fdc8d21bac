import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON>t, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>er2, HostL<PERSON>ener } from '@angular/core';
import { AuthService, UserProfile } from '../../core/services/auth.service';
import { GameService } from '../../core/services/game.service';
import { UserService } from '../../core/services/user.service';
import { UserSummaryService, UserSummary } from '../../core/services/user-summary.service';
import { ModalService } from '../../core/services/modal.service';
import { Router, ActivatedRoute, NavigationEnd } from '@angular/router';
import { Game } from '../../core/models/game.model';
import { User, UserFilters } from '../../core/models/user.model';
import { Subscription } from 'rxjs';
import { filter } from 'rxjs/operators';

@Component({
  selector: 'app-profile',
  standalone: false,
  templateUrl: './profile.html',
  styleUrl: './profile.css'
})
export class Profile implements OnI<PERSON>t, OnD<PERSON>roy {
  userProfile: UserProfile | null = null;
  isLoading = true;
  errorMessage = '';

  // User summary data
  userSummary: UserSummary | null = null;
  private summarySubscription?: Subscription;
  private routerSubscription?: Subscription;

  // Mobile sidebar state
  isMobileSidebarOpen = false;

  // Tab management
  activeTab: string = 'settings';

  // Tab definitions
  tabs = [
    {
      id: 'settings',
      label: 'ПАРАМЕТРЫ ПРОФИЛЯ'
    },
    {
      id: 'library',
      label: 'МОИ ИГРЫ'
    },
    {
      id: 'purchases',
      label: 'ИСТОРИЯ ПОКУПОК'
    }
  ];

  constructor(
    private authService: AuthService,
    private userService: UserService,
    private userSummaryService: UserSummaryService,
    private modalService: ModalService,
    private router: Router,
    private route: ActivatedRoute,
    private renderer: Renderer2
  ) {}

  ngOnInit(): void {
    this.loadUserProfile();
    this.loadUserSummary();
    this.setupRouterSubscription();
    // Set default tab to settings
    this.activeTab = 'settings';
  }

  ngOnDestroy(): void {
    // Clean up subscriptions
    if (this.summarySubscription) {
      this.summarySubscription.unsubscribe();
    }
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }
    // Ensure body scroll is restored
    this.renderer.removeClass(document.body, 'mobile-sidebar-open');
  }

  loadUserProfile(): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.authService.getUserProfile().subscribe({
      next: (profile) => {
        this.userProfile = profile;
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = error.message || 'Failed to load profile';
        this.isLoading = false;
      }
    });
  }

  loadUserSummary(): void {
    // Subscribe to the summary observable for real-time updates
    this.summarySubscription = this.userSummaryService.summary$.subscribe(summary => {
      this.userSummary = summary;
    });

    // Load initial summary data
    this.userSummaryService.getUserSummary().subscribe({
      next: (summary) => {
        // Summary is automatically updated via the subscription above
      },
      error: (error) => {
        console.error('Failed to load user summary:', error);
        // Don't show error to user as this is supplementary data
      }
    });
  }

  onLogout(): void {
    this.authService.logout();
  }

  refreshProfile(): void {
    this.loadUserProfile();
    this.refreshUserSummary();
  }

  refreshUserSummary(): void {
    this.userSummaryService.refreshSummary();
  }

  // Setup router subscription to close sidebar on navigation
  setupRouterSubscription(): void {
    this.routerSubscription = this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe(() => {
        this.closeMobileSidebar();
      });
  }

  // Mobile sidebar methods
  toggleMobileSidebar(): void {
    this.isMobileSidebarOpen = !this.isMobileSidebarOpen;
    this.updateBodyScroll();
  }

  closeMobileSidebar(): void {
    this.isMobileSidebarOpen = false;
    this.updateBodyScroll();
  }

  // Update body scroll based on sidebar state
  private updateBodyScroll(): void {
    if (this.isMobileSidebarOpen) {
      this.renderer.addClass(document.body, 'mobile-sidebar-open');
    } else {
      this.renderer.removeClass(document.body, 'mobile-sidebar-open');
    }
  }

  // Handle escape key to close mobile sidebar
  @HostListener('document:keydown.escape')
  onEscapeKey(): void {
    if (this.isMobileSidebarOpen) {
      this.closeMobileSidebar();
    }
  }

  verifyToken(): void {
    this.authService.verifyToken().subscribe({
      next: (response) => {
        console.log('Token verified:', response);
        this.modalService.success('Успех', 'Токен действителен!');
      },
      error: (error) => {
        console.error('Token verification failed:', error);
        this.modalService.error('Ошибка', 'Проверка токена не удалась: ' + error.message);
      }
    });
  }
  // Tab management methods
  setActiveTab(tabId: string): void {
    this.activeTab = tabId;
  }

  // Check if user has admin access
  hasAdminAccess(): boolean {
    return this.userProfile?.is_staff || false;
  }





}
