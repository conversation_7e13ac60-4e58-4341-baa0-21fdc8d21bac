/* Profile-specific styles */
.animated-gradient {
  background: linear-gradient(45deg, #1e293b, #334155, #0f172a, #1e293b);
  background-size: 400% 400%;
  animation: gradientShift 12s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.network-animation {
  animation: networkPulse 4s ease-in-out infinite;
}

@keyframes networkPulse {
  0%, 100% { opacity: 0.2; }
  50% { opacity: 0.4; }
}

/* Responsive layout styles */
.profile-layout {
  transition: all 0.3s ease;
}

/* Mobile sidebar animations */
.profile-sidebar {
  transition: transform 0.3s ease-in-out;
}

/* Mobile-first responsive design */
@media (max-width: 1023px) {
  .profile-layout {
    flex-direction: column;
    height: calc(100vh - 5rem);
  }

  .profile-sidebar {
    /* Mobile sidebar is fixed and hidden by default */
    position: fixed;
    top: 5rem;
    left: 0;
    width: 320px;
    height: calc(100vh - 5rem);
    z-index: 50;
    transform: translateX(-100%);
    border-right: 1px solid rgba(71, 85, 105, 0.3);
    border-bottom: none;
  }

  .profile-sidebar.translate-x-0 {
    transform: translateX(0);
  }

  .profile-sidebar.-translate-x-full {
    transform: translateX(-100%);
  }

  .profile-content {
    width: 100%;
    height: calc(100vh - 5rem);
    overflow-y: auto;
  }

  /* Add top padding to profile content on mobile only when admin sidebar is present */
  .profile-content > div:last-child {
    padding-top: 0; /* No extra padding needed for tab layout */
  }

  /* Mobile tab navigation */
  .tab-navigation {
    position: sticky;
    top: 0;
    z-index: 30;
  }

  .tab-button {
    font-size: 0.75rem;
    padding: 0.75rem 1rem;
  }

  /* Compact navigation on mobile */
  .profile-sidebar .space-y-4 > div {
    margin-bottom: 1rem;
  }

  .profile-sidebar h2 {
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
  }

  .profile-sidebar a {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
  }
}

/* Desktop layout */
@media (min-width: 1024px) {
  .profile-layout {
    flex-direction: row;
    height: calc(100vh - 5rem);
  }

  .profile-sidebar {
    /* Desktop sidebar is fixed and always visible for admin users only */
    position: fixed;
    top: 5rem;
    left: 0;
    transform: translateX(0) !important;
    width: 320px;
    height: calc(100vh - 5rem);
    border-right: 1px solid rgba(71, 85, 105, 0.3);
    border-bottom: none;
  }

  .profile-content {
    flex: 1;
    height: calc(100vh - 5rem);
    overflow-y: auto;
  }

  /* Hide mobile header on desktop */
  .mobile-header {
    display: none;
  }
}

/* Tab navigation styles */
.tab-navigation {
  background: rgba(30, 41, 59, 0.5);
  backdrop-filter: blur(12px);
  border-bottom: 1px solid rgba(71, 85, 105, 0.3);
}

.tab-button {
  transition: all 0.3s ease;
  position: relative;
}

.tab-button:hover {
  background-color: rgba(71, 85, 105, 0.5);
}

.tab-button.active {
  background-color: rgba(37, 99, 235, 0.8);
  color: white;
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #60a5fa;
}

/* Mobile overlay styles */
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: 40;
  transition: opacity 0.3s ease-in-out;
}

/* Mobile header styles */
.mobile-header {
  background: linear-gradient(to bottom, rgba(30, 41, 59, 0.95), rgba(30, 58, 138, 0.95));
  backdrop-filter: blur(12px);
}

/* Grid responsive utilities */
.profile-grid {
  display: grid;
  gap: 1.5rem;
}

@media (max-width: 640px) {
  .profile-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  .profile-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
  }
}

@media (min-width: 1025px) {
  .profile-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }
}

/* Custom scrollbar for sidebar */
.profile-sidebar::-webkit-scrollbar {
  width: 6px;
}

.profile-sidebar::-webkit-scrollbar-track {
  background: rgba(51, 65, 85, 0.1);
}

.profile-sidebar::-webkit-scrollbar-thumb {
  background: rgba(51, 65, 85, 0.3);
  border-radius: 3px;
}

.profile-sidebar::-webkit-scrollbar-thumb:hover {
  background: rgba(51, 65, 85, 0.5);
}

/* Enhanced input field styles */
.profile-input {
  transition: all 0.3s ease;
}

/* Button hover effects */
.profile-button {
  transition: all 0.3s ease;
}

/* Additional responsive utilities */
@media (max-width: 1023px) {
  /* Smaller text and spacing on mobile */
  .text-3xl { font-size: 1.875rem; }
  .text-2xl { font-size: 1.5rem; }
  .text-xl { font-size: 1.25rem; }
  .text-lg { font-size: 1.125rem; }

  /* Compact buttons on mobile */
  .profile-button {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }

  /* Reduced margins and padding */
  .space-y-6 > * + * { margin-top: 1rem; }
  .space-y-4 > * + * { margin-top: 0.75rem; }

  /* Mobile-friendly form elements */
  input, select, textarea {
    font-size: 16px; /* Prevents zoom on iOS */
  }
}

/* Touch-friendly interactive elements */
@media (hover: none) and (pointer: coarse) {
  .profile-sidebar a,
  .profile-button,
  button {
    min-height: 44px; /* iOS recommended touch target size */
    display: flex;
    align-items: center;
  }

  /* Remove hover effects on touch devices */
  .profile-sidebar a:hover,
  .profile-button:hover,
  button:hover {
    transform: none;
  }
}

/* Smooth section transitions */
.profile-section {
  transition: all 0.3s ease;
}

/* Prevent body scroll when mobile sidebar is open */
body.mobile-sidebar-open {
  overflow: hidden;
  position: fixed;
  width: 100%;
  height: 100%;
}

/* Mobile sidebar specific styles */
@media (max-width: 1023px) {
  .profile-sidebar {
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);
  }

  /* Smooth animations for mobile sidebar */
  .profile-sidebar.translate-x-0 {
    transform: translateX(0);
  }

  .profile-sidebar.-translate-x-full {
    transform: translateX(-100%);
  }

  /* Mobile header button styles */
  .mobile-header button {
    transition: all 0.2s ease;
  }

  .mobile-header button:hover {
    background-color: rgba(71, 85, 105, 0.3);
    border-radius: 0.5rem;
  }

  .mobile-header button:active {
    transform: scale(0.95);
  }
}

/* Ensure proper z-index stacking */
.mobile-overlay {
  z-index: 40;
}

.profile-sidebar {
  z-index: 50;
}

.mobile-header {
  z-index: 30;
}
